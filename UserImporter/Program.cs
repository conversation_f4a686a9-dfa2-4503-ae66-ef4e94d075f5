using OfficeOpenXml;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace UserImporter;

public class UserCreateRequest
{
    public string companyId { get; set; } = "0193b5af-8c63-7a75-95b2-ded5883c7a2f";
    public string[] roleIds { get; set; } = ["01981e19-92c1-78f1-bead-c4cd8a939771"];
    public string prefix { get; set; } = "";
    public string firstName { get; set; } = "";
    public string surname { get; set; } = "";
    public string suffix { get; set; } = "";
    public string email { get; set; } = "";
    public string jobTitle { get; set; } = "";
    public bool isInternal { get; set; } = false;
    public bool suppressEmail { get; set; } = false;
}

class Program
{
    private static readonly HttpClient httpClient = new HttpClient();
    private const string API_ENDPOINT = "https://user-apollo.production.live.dehavilland.co.uk/user-api/users";

    static async Task Main(string[] args)
    {
        Console.WriteLine("User Importer - Starting...");

        // Note: EPPlus license will be handled in the ExcelPackage constructor

        // Check for dry-run mode
        bool dryRun = args.Contains("--dry-run") || args.Contains("-d");

        try
        {
            var users = ReadUsersFromExcel("Apollo user list.xlsx");
            Console.WriteLine($"Found {users.Count} users to import.");

            if (dryRun)
            {
                Console.WriteLine("\n=== DRY RUN MODE - No API calls will be made ===");
                foreach (var user in users)
                {
                    Console.WriteLine($"Would create user: {user.firstName} {user.surname} ({user.email})");
                }
                Console.WriteLine($"\nDry run completed. {users.Count} users would be created.");
            }
            else
            {
                Console.WriteLine("\n=== LIVE MODE - Making API calls ===");
                Console.WriteLine("Press 'y' to continue or any other key to cancel...");
                var key = Console.ReadKey();
                Console.WriteLine();

                if (key.KeyChar != 'y' && key.KeyChar != 'Y')
                {
                    Console.WriteLine("Operation cancelled.");
                    return;
                }

                int successCount = 0;
                int errorCount = 0;

                foreach (var user in users)
                {
                    try
                    {
                        Console.WriteLine($"Creating user: {user.firstName} {user.surname} ({user.email})");
                        await CreateUser(user);
                        successCount++;
                        Console.WriteLine("✓ User created successfully");
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Console.WriteLine($"✗ Error creating user: {ex.Message}");
                    }

                    // Add a small delay to avoid overwhelming the API
                    await Task.Delay(1000);
                }

                Console.WriteLine($"\nImport completed:");
                Console.WriteLine($"✓ Successfully created: {successCount} users");
                Console.WriteLine($"✗ Errors: {errorCount} users");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error: {ex.Message}");
        }

        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }

    static List<UserCreateRequest> ReadUsersFromExcel(string filePath)
    {
        var users = new List<UserCreateRequest>();

        // Set EPPlus license before creating package
        try
        {
            // Try to set license using the static property
            var licenseProperty = typeof(ExcelPackage).GetProperty("License");
            if (licenseProperty != null)
            {
                var licenseObject = licenseProperty.GetValue(null);
                var setLicenseMethod = licenseObject?.GetType().GetMethod("SetLicense", new[] { typeof(string) });
                setLicenseMethod?.Invoke(licenseObject, new object[] { "NonCommercial" });
            }
        }
        catch
        {
            // Fallback: try environment variable
            Environment.SetEnvironmentVariable("EPPlusLicense", "NonCommercial");
        }

        var package = new ExcelPackage(new FileInfo(filePath));

        using (package)
        {
            var worksheet = package.Workbook.Worksheets[0]; // Use first worksheet

        // Determine the range of data
        var start = worksheet.Dimension.Start;
        var end = worksheet.Dimension.End;

        Console.WriteLine($"Reading Excel file: {filePath}");
        Console.WriteLine($"Worksheet: {worksheet.Name}");
        Console.WriteLine($"Data range: {start.Row},{start.Column} to {end.Row},{end.Column}");

        // Look for email addresses in the worksheet
        for (int row = start.Row; row <= end.Row; row++)
        {
            for (int col = start.Column; col <= end.Column; col++)
            {
                var cellValue = worksheet.Cells[row, col].Value?.ToString();

                if (!string.IsNullOrEmpty(cellValue) && IsValidEmail(cellValue))
                {
                    var user = ParseEmailToUser(cellValue);
                    if (user != null)
                    {
                        users.Add(user);
                        Console.WriteLine($"Found user: {user.firstName} {user.surname} ({user.email})");
                    }
                }
            }
        }

            return users;
        }
    }

    static bool IsValidEmail(string email)
    {
        // Basic email validation and check for name.surname@domain format
        var emailPattern = @"^[a-zA-Z]+\.[a-zA-Z]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        return Regex.IsMatch(email, emailPattern);
    }

    static UserCreateRequest? ParseEmailToUser(string email)
    {
        try
        {
            // Extract name and surname from email format: <EMAIL>
            var atIndex = email.IndexOf('@');
            if (atIndex == -1) return null;

            var localPart = email.Substring(0, atIndex);
            var dotIndex = localPart.IndexOf('.');
            if (dotIndex == -1) return null;

            var firstName = localPart.Substring(0, dotIndex);
            var surname = localPart.Substring(dotIndex + 1);

            // Capitalize first letter of each name
            firstName = CapitalizeFirstLetter(firstName);
            surname = CapitalizeFirstLetter(surname);

            return new UserCreateRequest
            {
                firstName = firstName,
                surname = surname,
                email = email
            };
        }
        catch
        {
            return null;
        }
    }

    static string CapitalizeFirstLetter(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        return char.ToUpper(input[0]) + input.Substring(1).ToLower();
    }

    static async Task CreateUser(UserCreateRequest user)
    {
        var json = JsonSerializer.Serialize(user, new JsonSerializerOptions
        {
            WriteIndented = true
        });

        Console.WriteLine($"Sending request: {json}");

        var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

        var response = await httpClient.PostAsync(API_ENDPOINT, content);

        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            throw new Exception($"API request failed with status {response.StatusCode}: {errorContent}");
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        Console.WriteLine($"API Response: {responseContent}");
    }
}