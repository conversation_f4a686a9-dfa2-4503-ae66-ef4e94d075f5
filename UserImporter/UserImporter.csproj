<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <None Update="Apollo user list.xlsx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="EPPlus" Version="8.0.8" />
      <PackageReference Include="System.Text.Json" Version="9.0.8" />
    </ItemGroup>

</Project>
